<template>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: isCollapsed }">
      <!-- Logo区域 -->
      <div class="logo-container">
        <div class="logo-icon">
          📚
        </div>
        <transition name="fade">
          <span v-if="!isCollapsed" class="logo-text">书籍管理系统</span>
        </transition>
      </div>
      
      <!-- 导航菜单 -->
      <nav class="nav-menu">
        <router-link 
          v-for="item in menuItems" 
          :key="item.path" 
          :to="item.path" 
          class="nav-item"
          :class="{ active: $route.path === item.path }"
        >
          <i class="nav-icon" v-html="item.icon"></i>
          <transition name="fade">
            <span v-if="!isCollapsed" class="nav-text">{{ item.title }}</span>
          </transition>
        </router-link>
      </nav>
      
      <!-- 折叠按钮 -->
      <div class="sidebar-toggle" @click="toggleSidebar">
        <i class="toggle-icon">{{ isCollapsed ? '→' : '←' }}</i>
      </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-wrapper">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <h1 class="page-title">{{ currentPageTitle }}</h1>
          <div class="breadcrumb">
            <span class="breadcrumb-item">控制台</span>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item active">{{ currentPageTitle }}</span>
          </div>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <div class="user-info">
              <div class="user-avatar">👤</div>
              <span class="user-name">管理员</span>
            </div>
          </div>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <main class="page-content">
        <div class="content-container">
          <router-view v-slot="{ Component }">
            <transition name="fade-slide" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isCollapsed = ref(false)

// 菜单项配置
const menuItems = [
  {
    path: '/books',
    title: '书籍管理',
    icon: '📖'
  },
  {
    path: '/categories', 
    title: '分类管理',
    icon: '📂'
  },
  {
    path: '/test',
    title: 'API测试',
    icon: '🔧'
  }
]

// 当前页面标题
const currentPageTitle = computed(() => {
  const currentItem = menuItems.find(item => item.path === route.path)
  return currentItem?.title || '控制台'
})

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}
</script>

<style>
/* Google Fonts - Roboto */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

/* 全局重置 & Material Design 基础 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  overflow-x: auto;
}

body {
  min-width: 1200px;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: #f5f7fa; /* Material 背景色 */
  color: #3c4043; /* Material 文本颜色 */
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 应用容器 */
.app-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
}

/* 侧边栏 */
.sidebar {
  width: 256px; /* Material 标准宽度 */
  background: #ffffff;
  border-right: 1px solid #e0e0e0;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1000;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  width: 72px; /* Material 折叠宽度 */
}

/* Logo区域 */
.logo-container {
  height: 64px; /* Material 顶部栏高度 */
  padding: 0 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.logo-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a73e8; /* Google Blue */
  border-radius: 50%;
  color: white;
}

.logo-text {
  font-size: 18px;
  font-weight: 500;
  color: #3c4043;
  white-space: nowrap;
}

/* 导航菜单 */
.nav-menu {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-grow: 1;
}

.nav-item {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  border-radius: 24px; /* Material 3 风格圆角 */
  text-decoration: none;
  color: #3c4043;
  transition: background-color 0.2s ease-in-out;
  font-weight: 500;
  overflow: hidden;
}

.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 0;
}

.nav-item:hover {
  background-color: #f1f3f4;
}

.nav-item.active {
  background-color: #e8f0fe; /* Google Blue 浅色背景 */
  color: #1967d2; /* Google Blue 深色文字 */
}

.nav-icon {
  font-size: 22px;
  margin-right: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar.collapsed .nav-icon {
  margin-right: 0;
}

.nav-text {
  font-size: 14px;
  white-space: nowrap;
}

/* 折叠按钮 */
.sidebar-toggle {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: #f1f3f4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #5f6368;
  transition: all 0.2s ease-in-out;
}

.sidebar-toggle:hover {
  background: #e8eaed;
}

/* 主内容区 */
.main-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f5f7fa;
  min-width: 0;
  overflow: hidden;
}

/* 顶部导航栏 */
.top-header {
  height: 64px;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 100;
  flex-shrink: 0;
}

.page-title {
  font-size: 22px;
  font-weight: 400;
  color: #202124;
  margin: 0;
}

.breadcrumb {
  display: none; /* Material Design 通常不使用面包屑 */
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: #e8f0fe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1967d2;
  font-size: 16px;
  font-weight: 500;
}

.user-name {
  font-weight: 500;
  font-size: 14px;
  color: #3c4043;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 24px;
  overflow: auto;
  min-width: 0;
}

/* Element Plus 覆盖样式 */
.el-button {
  border-radius: 4px; /* Material 按钮圆角 */
  font-weight: 500;
  text-transform: uppercase; /* Material 按钮文字大写 */
  letter-spacing: 0.5px;
}

.el-button--primary {
  background: #1a73e8;
  border: none;
}

.el-button--primary:hover {
  background: #1867c0;
}

.el-table {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.el-dialog {
  border-radius: 8px;
}

.el-input__wrapper {
  border-radius: 4px;
  background: #f1f3f4;
  border-color: transparent;
  box-shadow: none !important;
}

.el-input__wrapper.is-focus {
  background: white;
  border-color: #1a73e8;
}

.el-select .el-input__wrapper {
  background: #f1f3f4;
  border-color: transparent;
  box-shadow: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-title {
    font-size: 20px;
  }
  .user-name {
    display: none;
  }
  .top-header {
    padding: 0 16px;
  }
  .page-content {
    padding: 16px;
  }
}
</style>
