// 环境配置
export const config = {
  // API基础地址
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  
  // 环境信息
  envName: import.meta.env.VITE_ENV_NAME,
  envLabel: import.meta.env.VITE_ENV_LABEL,
  
  // 是否为开发环境
  isDev: import.meta.env.DEV,
  
  // 是否为生产环境
  isProd: import.meta.env.PROD,
  
  // 上传文件大小限制 (MB)
  maxFileSize: {
    pdf: 50, // PDF文件最大50MB
    image: 5, // 图片文件最大5MB
  },
  
  // 支持的文件类型
  allowedFileTypes: {
    pdf: ['.pdf'],
    image: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  },
  
  // 加密配置
  encryption: {
    key: import.meta.env.VITE_ENCRYPTION_KEY || 'CyFB>j7~6#fW(u}{?/Xhd)T@kmMP54`v', // 32字节密钥
  },
}

// 环境信息打印
console.log('🌍 当前环境信息:')
console.log(`  环境: ${config.envLabel} (${config.envName})`)
console.log(`  API地址: ${config.apiBaseUrl}`)
console.log(`  开发模式: ${config.isDev}`)
console.log(`  生产模式: ${config.isProd}`)

// 环境验证
if (!config.apiBaseUrl) {
  console.error('❌ 错误: API基础地址未配置！')
} 