import { config } from '../config/env'

/**
 * 测试密钥配置
 */
export function testKeyConfig() {
  console.log('🔑 测试密钥配置...')
  
  const key = config.encryption.key
  const keyLength = key.length
  
  console.log('密钥:', key)
  console.log('密钥长度:', keyLength)
  console.log('是否满足32字符要求:', keyLength === 32)
  
  if (keyLength !== 32) {
    console.error('❌ 密钥长度不正确！需要32字符，当前:', keyLength)
    return false
  }
  
  console.log('✅ 密钥配置正确')
  return true
} 