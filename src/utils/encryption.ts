import CryptoJS from 'crypto-js'
import { config } from '../config/env'

/**
 * 加密工具类
 * 实现与Dart端对应的AES-CBC加密逻辑
 */
export class EncryptionUtils {
  // 加密密钥 - 需要与Dart端保持一致
  private static readonly ENCRYPTION_KEY = config.encryption.key

  /**
   * 加密文件数据
   * @param fileData 文件数据（ArrayBuffer或Uint8Array）
   * @returns 加密后的数据（Uint8Array）
   */
  static async encryptFile(fileData: ArrayBuffer | Uint8Array): Promise<Uint8Array> {
    // 将文件数据转换为WordArray
    const wordArray = CryptoJS.lib.WordArray.create(fileData)

    // 生成16字节的随机IV
    const iv = CryptoJS.lib.WordArray.random(16)

    // 显式将密钥字符串转换为WordArray，确保与Flutter端兼容
    const key = CryptoJS.enc.Utf8.parse(this.ENCRYPTION_KEY)

    // 创建AES加密器（CBC模式）
    const encrypted = CryptoJS.AES.encrypt(wordArray, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })

    // 将IV和加密数据合并
    const ivBytes = this.wordArrayToUint8Array(iv)
    const encryptedBytes = this.wordArrayToUint8Array(encrypted.ciphertext)

    // 创建最终的数据数组：IV(16字节) + 加密数据
    const result = new Uint8Array(ivBytes.length + encryptedBytes.length)
    result.set(ivBytes, 0)
    result.set(encryptedBytes, ivBytes.length)

    return result
  }

  /**
   * 加密File对象
   * @param file 要加密的文件
   * @returns 加密后的File对象
   */
  static async encryptFileObject(file: File): Promise<File> {
    try {
      console.log('🔐 开始加密文件:', file.name, '大小:', file.size, '字节')
      
      // 验证密钥
      if (!this.validateKey()) {
        throw new Error('加密密钥长度不正确，需要32字符长度')
      }
      
      // 读取文件数据
      const arrayBuffer = await file.arrayBuffer()
      console.log('📄 文件读取完成，开始加密...')
      
      // 加密数据
      const encryptedData = await this.encryptFile(arrayBuffer)
      console.log('🔒 文件加密完成，加密后大小:', encryptedData.length, '字节')
      
      // 创建新的Blob对象
      const encryptedBlob = new Blob([encryptedData], { type: file.type })
      
      // 创建新的File对象，保持原始文件名
      const encryptedFile = new File([encryptedBlob], file.name, {
        type: file.type,
        lastModified: file.lastModified
      })
      
      console.log('✅ 加密文件创建完成:', encryptedFile.name)
      return encryptedFile
      
    } catch (error) {
      console.error('❌ 文件加密失败:', error)
      throw new Error(`文件加密失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 将CryptoJS WordArray转换为Uint8Array
   * @param wordArray CryptoJS WordArray
   * @returns Uint8Array
   */
  private static wordArrayToUint8Array(wordArray: CryptoJS.lib.WordArray): Uint8Array {
    const words = wordArray.words
    const sigBytes = wordArray.sigBytes
    const result = new Uint8Array(sigBytes)
    
    for (let i = 0; i < sigBytes; i++) {
      const byte = (words[Math.floor(i / 4)] >>> (24 - (i % 4) * 8)) & 0xff
      result[i] = byte
    }
    
    return result
  }

  /**
   * 验证加密密钥长度
   * @returns 密钥是否有效
   */
  static validateKey(): boolean {
    const keyBytes = CryptoJS.enc.Utf8.parse(this.ENCRYPTION_KEY)
    return keyBytes.sigBytes === 32 // AES-256需要32字节密钥
  }

  /**
   * 测试加密解密兼容性（用于验证与Flutter端的兼容性）
   * @param testData 测试数据
   * @returns 测试结果
   */
  static async testEncryptionCompatibility(testData: string = 'Hello Flutter Compatibility Test!'): Promise<{
    success: boolean;
    originalData: string;
    encryptedSize: number;
    keyInfo: {
      key: string;
      keyLength: number;
      keyBytes: number;
    };
    ivHex: string;
    encryptedDataHex: string;
  }> {
    try {
      console.log('🧪 开始兼容性测试...')

      // 创建测试文件
      const testBytes = new TextEncoder().encode(testData)
      const testBlob = new Blob([testBytes], { type: 'application/pdf' })
      const testFile = new File([testBlob], 'test.pdf', { type: 'application/pdf' })

      // 加密文件
      const encryptedFile = await this.encryptFileObject(testFile)
      const encryptedBytes = new Uint8Array(await encryptedFile.arrayBuffer())

      // 提取IV和密文（用于Flutter端验证）
      const iv = encryptedBytes.slice(0, 16)
      const ciphertext = encryptedBytes.slice(16)

      // 转换为十六进制字符串（便于调试）
      const ivHex = Array.from(iv).map(b => b.toString(16).padStart(2, '0')).join('')
      const encryptedDataHex = Array.from(ciphertext).map(b => b.toString(16).padStart(2, '0')).join('')

      const keyBytes = CryptoJS.enc.Utf8.parse(this.ENCRYPTION_KEY)

      const result = {
        success: true,
        originalData: testData,
        encryptedSize: encryptedBytes.length,
        keyInfo: {
          key: this.ENCRYPTION_KEY,
          keyLength: this.ENCRYPTION_KEY.length,
          keyBytes: keyBytes.sigBytes
        },
        ivHex,
        encryptedDataHex: encryptedDataHex.substring(0, 64) + '...' // 只显示前32字节
      }

      console.log('✅ 兼容性测试完成:', result)
      return result

    } catch (error) {
      console.error('❌ 兼容性测试失败:', error)
      return {
        success: false,
        originalData: testData,
        encryptedSize: 0,
        keyInfo: {
          key: this.ENCRYPTION_KEY,
          keyLength: this.ENCRYPTION_KEY.length,
          keyBytes: 0
        },
        ivHex: '',
        encryptedDataHex: ''
      }
    }
  }
}