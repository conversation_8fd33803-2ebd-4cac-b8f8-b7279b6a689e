import { EncryptionUtils } from './encryption'

/**
 * 简单的加密测试
 * 用于验证加密功能是否正常工作
 */
export async function testEncryption() {
  console.log('🔐 开始测试加密功能...')
  
  try {
    // 测试密钥验证
    const isKeyValid = EncryptionUtils.validateKey()
    console.log('✅ 密钥验证:', isKeyValid ? '通过' : '失败')
    
    if (!isKeyValid) {
      console.error('❌ 密钥长度不正确，需要32字符长度')
      console.error('当前密钥长度:', import.meta.env.VITE_ENCRYPTION_KEY?.length || '未设置')
      return false
    }
    
    // 创建测试数据
    const testData = new TextEncoder().encode('Hello, this is a test PDF content!')
    const testBlob = new Blob([testData], { type: 'application/pdf' })
    const testFile = new File([testBlob], 'test.pdf', { type: 'application/pdf' })
    
    console.log('📄 原始文件大小:', testFile.size, '字节')
    
    // 测试加密
    const encryptedFile = await EncryptionUtils.encryptFileObject(testFile)
    console.log('🔒 加密后文件大小:', encryptedFile.size, '字节')
    
    // 验证加密后的文件比原文件大（因为包含了IV）
    if (encryptedFile.size > testFile.size) {
      console.log('✅ 加密成功：文件大小增加（包含IV）')
      return true
    } else {
      console.error('❌ 加密失败：文件大小异常')
      return false
    }
    
  } catch (error) {
    console.error('❌ 加密测试失败:', error)
    return false
  }
}

/**
 * 测试与Flutter端的兼容性
 */
export async function testFlutterCompatibility(): Promise<boolean> {
  try {
    console.log('🔄 开始测试Flutter兼容性...')

    const result = await EncryptionUtils.testEncryptionCompatibility()


    if (result.success) {
      console.log('📋 兼容性测试结果:')
      console.log('  原始数据:', result.originalData)
      console.log('  加密后大小:', result.encryptedSize, '字节')
      console.log('  密钥信息:', result.keyInfo)
      console.log('  IV (hex):', result.ivHex)
      console.log('  密文前缀 (hex):', result.encryptedDataHex)
      console.log('')
      console.log('🔧 Flutter解密代码示例:')
      console.log(`  final key = '${result.keyInfo.key}';`)
      console.log('  final iv = encrypt.IV.fromBase16(\'' + result.ivHex + '\');')
      console.log('  // 使用您现有的解密方法即可')

      return true
    } else {
      console.error('❌ Flutter兼容性测试失败')
      return false
    }

  } catch (error) {
    console.error('❌ Flutter兼容性测试异常:', error)
    return false
  }
}