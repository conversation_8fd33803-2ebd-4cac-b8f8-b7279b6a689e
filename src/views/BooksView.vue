<template>
  <div class="books-container">
    <!-- 书籍列表 -->
    <div class="table-container material-card">
      <div class="table-header">
        <div class="header-info">
          <el-tag type="info" size="small" class="encryption-tag">
            <span class="encryption-icon">🔒</span>
            PDF文件自动加密
          </el-tag>
          <el-button v-if="isDev" size="small" type="warning" @click="testEncryptionFunction" class="test-button">
            🧪 测试加密
          </el-button>
          <el-button v-if="isDev" size="small" type="info" @click="testKeyConfigFunction" class="test-button">
            🔑 测试密钥
          </el-button>
          <el-button v-if="isDev" size="small" type="success" @click="testFlutterCompatibilityFunction" class="test-button">
            🔄 Flutter兼容性
          </el-button>
        </div>
        <el-button type="primary" @click="openCreateDialog" class="upload-button">
          <span class="upload-icon">📚</span>
          <span class="upload-text">上传书籍</span>
        </el-button>
      </div>
      <el-table :data="books" style="width: 100%" v-loading="loading">
        <el-table-column prop="title" label="名称" min-width="200" show-overflow-tooltip />
        <el-table-column label="封面" width="100" align="center">
          <template #default="scope">
            <el-image v-if="scope.row.cover" :src="scope.row.cover" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;" fit="cover" />
            <span v-else class="no-cover">无</span>
          </template>
        </el-table-column>
        <el-table-column label="分类" width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ getCategoryName(scope.row.categoryId) }}
          </template>
        </el-table-column>
        <el-table-column prop="level" label="Level" width="80" align="center" />
        <el-table-column prop="isFree" label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isFree ? 'success' : 'warning'" size="small">
              {{ scope.row.isFree ? '免费' : '付费' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="更新时间" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDate(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="scope">
            <el-button text size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button text type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑书籍对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑书籍' : '上传书籍'" width="600px" @close="resetForm">
      <el-form :model="form" label-width="100px" :rules="formRules" ref="formRef">
        <el-form-item label="名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入书籍名称" />
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%">
            <el-option v-for="category in categories" :key="category.id" :label="category.categoryName" :value="category.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="Level" prop="level">
          <el-select v-model="form.level" placeholder="请选择Level" style="width: 100%">
            <el-option label="1" :value="1" />
            <el-option label="2" :value="2" />
            <el-option label="3" :value="3" />
            <el-option label="4" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="PDF文件" prop="pdfFile">
          <el-upload ref="pdfUploadRef" action="#" :auto-upload="false" :on-change="handlePdfChange" :on-remove="handlePdfRemove" :file-list="pdfFileList" accept=".pdf" style="width: 100%" :limit="1">
            <el-button size="small" type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传 PDF 文件，最大50MB</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="封面文件" prop="coverFile">
          <el-upload ref="coverUploadRef" action="#" :auto-upload="false" :on-change="handleCoverChange" :on-remove="handleCoverRemove" :file-list="coverFileList" accept="image/*" style="width: 100%" :limit="1">
            <el-button size="small" type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传 JPG/PNG 文件，最大5MB</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="是否免费" prop="isFree">
          <el-switch v-model="form.isFree" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="uploading">
            {{ isEdit ? '更新' : '上传' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { bookApi, categoryApi, type Book, type Category } from '../services/api'
  import { testEncryption, testFlutterCompatibility } from '../utils/encryption.test'
  import { testKeyConfig } from '../utils/key-test'

  const dialogVisible = ref(false)
  const loading = ref(false)
  const uploading = ref(false)
  const isEdit = ref(false)
  const isDev = import.meta.env.DEV
  const formRef = ref()
  const pdfUploadRef = ref()
  const coverUploadRef = ref()

  const books = ref<Book[]>([])
  const categories = ref<Category[]>([])
  const pdfFileList = ref<Array<{ raw: File;[key: string]: unknown }>>([])
  const coverFileList = ref<Array<{ raw: File;[key: string]: unknown }>>([])

  // 表单验证规则
  const formRules = ref<Record<string, Array<{ required: boolean; message: string; trigger: string }>>>({
    title: [{ required: true, message: '请输入书籍名称', trigger: 'blur' }],
    categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
    level: [{ required: true, message: '请选择Level', trigger: 'change' }],
  })

  const form = reactive({
    id: '',
    title: '',
    categoryId: '',
    level: 1,
    isFree: false,
    pdfFile: null as File | null,
    coverFile: null as File | null,
  })

  // 更新表单验证规则
  const updateFormRules = () => {
    const baseRules: Record<string, Array<{ required: boolean; message: string; trigger: string }>> = {
      title: [{ required: true, message: '请输入书籍名称', trigger: 'blur' }],
      categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
      level: [{ required: true, message: '请选择Level', trigger: 'change' }],
    }

    if (!isEdit.value) {
      // 创建模式下，文件是必需的
      baseRules.pdfFile = [{ required: true, message: '请上传PDF文件', trigger: 'change' }]
      baseRules.coverFile = [{ required: true, message: '请上传封面文件', trigger: 'change' }]
    } else {
      // 编辑模式下，只有在用户手动清除了文件时才要求重新上传
      // 检查是否有显示的文件，如果没有显示且没有选择新文件，则要求上传
      if (pdfFileList.value.length === 0 && !form.pdfFile) {
        baseRules.pdfFile = [{ required: true, message: 'PDF文件已被删除，请重新上传', trigger: 'change' }]
      }
      if (coverFileList.value.length === 0 && !form.coverFile) {
        baseRules.coverFile = [{ required: true, message: '封面文件已被删除，请重新上传', trigger: 'change' }]
      }
    }

    formRules.value = baseRules
  }

  // 获取书籍列表
  const fetchBooks = async () => {
    loading.value = true
    try {
      const response = await bookApi.getBookList()
      if (response.success) {
        books.value = response.data
      } else {
        ElMessage.error(response.message || '获取书籍列表失败')
      }
    } catch (error) {
      console.error('获取书籍列表失败:', error)
      ElMessage.error('获取书籍列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await categoryApi.getCategoryList()
      if (response.success) {
        categories.value = response.data
      } else {
        ElMessage.error(response.message || '获取分类列表失败')
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      ElMessage.error('获取分类列表失败')
    }
  }

  // 处理PDF文件选择
  const handlePdfChange = (file: { raw: File;[key: string]: unknown }) => {
    form.pdfFile = file.raw
    pdfFileList.value = [file]
    // 显示加密提示
    ElMessage.info('PDF文件将在上传时自动加密')
  }

  // 处理PDF文件删除
  const handlePdfRemove = () => {
    form.pdfFile = null
    pdfFileList.value = []
    // 更新验证规则
    updateFormRules()
    // 触发表单验证
    if (formRef.value) {
      // 强制重新验证PDF文件字段
      setTimeout(() => {
        formRef.value.validateField('pdfFile')
      }, 0)
    }
  }

  // 处理封面文件选择
  const handleCoverChange = (file: { raw: File;[key: string]: unknown }) => {
    form.coverFile = file.raw
    coverFileList.value = [file]
    // 更新验证规则
    updateFormRules()
  }

  // 处理封面文件删除
  const handleCoverRemove = () => {
    form.coverFile = null
    coverFileList.value = []
    // 更新验证规则
    updateFormRules()
    // 触发表单验证
    if (formRef.value) {
      // 强制重新验证封面文件字段
      setTimeout(() => {
        formRef.value.validateField('coverFile')
      }, 0)
    }
  }

  // 打开创建对话框
  const openCreateDialog = () => {
    isEdit.value = false
    resetForm() // 先重置表单
    updateFormRules() // 更新验证规则
    dialogVisible.value = true
  }

  // 编辑书籍
  const handleEdit = async (row: Book) => {
    console.log('编辑书籍数据:', row) // 调试信息
    console.log('isFree值:', row.isFree, typeof row.isFree) // 调试isFree字段
    isEdit.value = true
    form.id = row.id
    form.title = row.title
    form.categoryId = row.categoryId
    form.level = row.level
    form.isFree = Boolean(row.isFree) // 确保转换为布尔值
    form.pdfFile = null
    form.coverFile = null
    pdfFileList.value = []
    coverFileList.value = []

    // 如果有封面，显示当前封面
    if (row.cover) {
      console.log('设置封面文件:', row.cover) // 调试信息
      coverFileList.value = [{
        name: row.cover,
        url: row.cover,
        raw: null as unknown as File
      }]
    }

    // 如果有PDF文件，显示当前PDF
    if (row.url) {
      console.log('设置PDF文件:', row.url) // 调试信息
      // 从URL中提取文件名
      const fileName = row.url.split('/').pop() || 'document.pdf'
      pdfFileList.value = [{
        name: fileName,
        url: row.url,
        raw: null as unknown as File
      }]
    } else {
      console.log('没有PDF文件URL') // 调试信息
    }

    updateFormRules() // 更新验证规则
    dialogVisible.value = true
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
    } catch {
      return
    }

    // 检查文件是否上传
    if (!isEdit.value) {
      // 创建模式下，文件是必需的
      if (!form.pdfFile) {
        ElMessage.error('请上传PDF文件')
        return
      }

      if (!form.coverFile) {
        ElMessage.error('请上传封面文件')
        return
      }
    } else {
      // 编辑模式下，检查文件状态
      // 如果文件列表为空且没有选择新文件，说明用户删除了文件但没有重新上传
      if (pdfFileList.value.length === 0 && !form.pdfFile) {
        ElMessage.error('PDF文件已被删除，请重新上传PDF文件')
        return
      }

      if (coverFileList.value.length === 0 && !form.coverFile) {
        ElMessage.error('封面文件已被删除，请重新上传封面文件')
        return
      }
    }

    uploading.value = true
    try {
      let response
      if (isEdit.value) {
        // 更新书籍 - 只传递有值的字段
        const updateParams: Partial<{
          title: string;
          categoryId: string;
          isFree: boolean;
          level: number;
          pdfFile: File;
          coverFile: File;
        }> = {
          title: form.title,
          categoryId: form.categoryId,
          isFree: form.isFree,
          level: form.level,
        }

        // 只有当用户选择了新文件时才传递文件参数
        if (form.pdfFile) {
          updateParams.pdfFile = form.pdfFile
        }
        if (form.coverFile) {
          updateParams.coverFile = form.coverFile
        }

        response = await bookApi.updateBook(form.id, updateParams)
      } else {
        // 创建书籍
        response = await bookApi.uploadBook({
          title: form.title,
          categoryId: form.categoryId,
          isFree: form.isFree,
          level: form.level,
          pdfFile: form.pdfFile!,
          coverFile: form.coverFile!,
        })
      }

      if (response.success) {
        ElMessage.success(isEdit.value ? '书籍更新成功' : '书籍上传成功')
        dialogVisible.value = false
        resetForm()
        fetchBooks() // 刷新列表
      } else {
        ElMessage.error(response.message || (isEdit.value ? '书籍更新失败' : '书籍上传失败'))
      }
    } catch (error: unknown) {
      console.error('操作书籍失败:', error)
      const errorMessage = error instanceof Error ? error.message : (isEdit.value ? '书籍更新失败' : '书籍上传失败')
      ElMessage.error(errorMessage)
    } finally {
      uploading.value = false
    }
  }

  // 删除书籍
  const handleDelete = async (row: Book) => {
    try {
      await ElMessageBox.confirm('确定要删除这本书吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      const response = await bookApi.deleteBook(row.id)
      if (response.success) {
        ElMessage.success(response.message || '删除成功')
        fetchBooks() // 刷新列表
      } else {
        ElMessage.error(response.message || '删除失败')
      }
    } catch (error: unknown) {
      if (error !== 'cancel') {
        console.error('删除书籍失败:', error)
        const errorMessage = error instanceof Error ? error.message : '删除失败'
        ElMessage.error(errorMessage)
      }
    }
  }

  // 重置表单
  const resetForm = () => {
    form.id = ''
    form.title = ''
    form.categoryId = ''
    form.level = 1
    form.isFree = false
    form.pdfFile = null
    form.coverFile = null
    pdfFileList.value = []
    coverFileList.value = []
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  // 根据分类ID获取分类名称
  const getCategoryName = (categoryId: string) => {
    if (!categories.value || !Array.isArray(categories.value) || categories.value.length === 0) {
      return '未知分类'; // 如果分类数据未加载或为空，返回未知分类
    }
    const category = categories.value.find(cat => cat.id === categoryId)
    return category ? category.categoryName : '未知分类' // 如果找不到匹配的分类，返回未知分类
  }

  // 格式化日期
  const formatDate = (timestamp: string | number) => {
    if (!timestamp) return '-'

    let date: Date

    // 如果是ISO格式字符串 (如: 2025-06-29T12:24:31.000Z)
    if (typeof timestamp === 'string' && timestamp.includes('T')) {
      date = new Date(timestamp)
    } else {
      // 如果是数字时间戳，假设是秒级
      date = new Date(Number(timestamp) * 1000)
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-'
    }

    // 格式化为 yyyy-MM-dd HH:mm:ss
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 测试加密功能（仅在开发环境）
  const testEncryptionFunction = async () => {
    try {
      const success = await testEncryption()
      if (success) {
        ElMessage.success('加密功能测试通过')
      } else {
        ElMessage.error('加密功能测试失败')
      }
    } catch (error) {
      console.error('加密测试失败:', error)
      ElMessage.error('加密功能测试失败')
    }
  }

  // 测试密钥配置（仅在开发环境）
  const testKeyConfigFunction = () => {
    try {
      const success = testKeyConfig()
      if (success) {
        ElMessage.success('密钥配置正确')
      } else {
        ElMessage.error('密钥配置错误，请检查控制台')
      }
    } catch (error) {
      console.error('密钥测试失败:', error)
      ElMessage.error('密钥测试失败')
    }
  }

  // 测试Flutter兼容性（仅在开发环境）
  const testFlutterCompatibilityFunction = async () => {
    try {
      const success = await testFlutterCompatibility()
      if (success) {
        ElMessage.success('Flutter兼容性测试通过，请查看控制台获取详细信息')
      } else {
        ElMessage.error('Flutter兼容性测试失败')
      }
    } catch (error) {
      console.error('Flutter兼容性测试失败:', error)
      ElMessage.error('Flutter兼容性测试失败')
    }
  }

  // 组件挂载时获取数据
  onMounted(() => {
    fetchBooks()
    fetchCategories()
  })
</script>

<style scoped>

  /* Material Design 风格卡片 */
  .material-card {
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    /* Material Design Elevation 1 */
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .material-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04), 0 0 4px rgba(0, 0, 0, 0.06);
    /* Material Design Elevation 2 */
  }

  /* 容器 */
  .books-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
  }

  /* 表格容器 */
  .table-container {
    overflow: hidden;
    /* 确保表格内部滚动条正常 */
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .header-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .encryption-tag {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    color: #475569;
  }

  .encryption-icon {
    font-size: 14px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }

  .test-button {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    color: #92400e;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .test-button:hover {
    background: linear-gradient(135deg, #fde68a 0%, #fbbf24 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }

  .upload-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .upload-button:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
  }

  .upload-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
  }

  .upload-icon {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }

  .upload-text {
    font-size: 14px;
    letter-spacing: 0.5px;
  }

  .no-cover {
    color: #9aa0a6;
    font-size: 12px;
  }

  /* 对话框优化 */
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
  }

  .el-upload__tip {
    color: #64748b;
    font-size: 12px;
    margin-top: 8px;
  }
</style>
