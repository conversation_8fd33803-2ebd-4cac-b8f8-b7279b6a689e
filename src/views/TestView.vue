<template>
  <div class="test-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h2 class="header-title">API 测试</h2>
          <p class="header-desc">测试系统API连接和配置状态</p>
        </div>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon api-icon">🔧</div>
        <div class="stat-info">
          <div class="stat-number">2</div>
          <div class="stat-label">API接口</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon env-icon">⚙️</div>
        <div class="stat-info">
          <div class="stat-number">{{ currentMode }}</div>
          <div class="stat-label">运行环境</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon status-icon">✅</div>
        <div class="stat-info">
          <div class="stat-number">{{ isDev ? '开发' : '生产' }}</div>
          <div class="stat-label">模式状态</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon config-icon">📊</div>
        <div class="stat-info">
          <div class="stat-number">{{ config.apiBaseUrl ? '已配置' : '未配置' }}</div>
          <div class="stat-label">API配置</div>
        </div>
      </div>
    </div>
    
    <el-card class="test-card">
      <template #header>
        <span>分类API测试</span>
      </template>
      
      <el-button @click="testGetCategories" :loading="testingCategories">
        测试获取分类列表
      </el-button>
      
      <div v-if="categoriesResult" class="result">
        <h4>结果:</h4>
        <pre>{{ JSON.stringify(categoriesResult, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>书籍API测试</span>
      </template>
      
      <el-button @click="testGetBooks" :loading="testingBooks">
        测试获取书籍列表
      </el-button>
      
      <div v-if="booksResult" class="result">
        <h4>结果:</h4>
        <pre>{{ JSON.stringify(booksResult, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>API配置信息</span>
      </template>
      
      <div class="config-info">
        <p><strong>API基础地址:</strong> {{ config.apiBaseUrl }}</p>
        <p><strong>当前环境:</strong> {{ config.envLabel }} ({{ config.envName }})</p>
        <p><strong>开发模式:</strong> {{ config.isDev ? '是' : '否' }}</p>
        <p><strong>生产模式:</strong> {{ config.isProd ? '是' : '否' }}</p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { bookApi, categoryApi } from '../services/api'
import { config } from '../config/env'

const testingCategories = ref(false)
const testingBooks = ref(false)
const categoriesResult = ref<{ success: boolean; data: unknown[] } | { error: string } | null>(null)
const booksResult = ref<{ success: boolean; data: unknown[] } | { error: string } | null>(null)

// 环境变量
const currentMode = import.meta.env.MODE
const isDev = import.meta.env.DEV

// 测试获取分类列表
const testGetCategories = async () => {
  testingCategories.value = true
  try {
    const response = await categoryApi.getCategoryList()
    categoriesResult.value = response
    ElMessage.success(response.message || '分类API测试成功')
  } catch (error) {
    console.error('分类API测试失败:', error)
    categoriesResult.value = { error: error instanceof Error ? error.message : '未知错误' }
    ElMessage.error('分类API测试失败')
  } finally {
    testingCategories.value = false
  }
}

// 测试获取书籍列表
const testGetBooks = async () => {
  testingBooks.value = true
  try {
    const response = await bookApi.getBookList()
    booksResult.value = response
    ElMessage.success(response.message || '书籍API测试成功')
  } catch (error) {
    console.error('书籍API测试失败:', error)
    booksResult.value = { error: error instanceof Error ? error.message : '未知错误' }
    ElMessage.error('书籍API测试失败')
  } finally {
    testingBooks.value = false
  }
}
</script>

<style scoped>
/* 容器 */
.test-container {
  padding: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-desc {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 8px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.api-icon {
  background: linear-gradient(135deg, #48bb78, #38a169);
}

.env-icon {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.status-icon {
  background: linear-gradient(135deg, #48bb78, #38a169);
}

.config-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 测试卡片 */
.test-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
  overflow: hidden;
}

:deep(.el-card__header) {
  background: #f8fafc;
  padding: 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  font-weight: 600;
  color: #475569;
}

:deep(.el-card__body) {
  padding: 24px;
}

/* 结果显示 */
.result {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.result h4 {
  margin: 0 0 12px 0;
  color: #475569;
  font-weight: 600;
  font-size: 14px;
}

.result pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 12px;
  color: #334155;
  line-height: 1.5;
}

/* 配置信息 */
.config-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-info p {
  margin: 0;
  padding: 12px;
  background: #f1f5f9;
  border-radius: 8px;
  font-size: 14px;
  color: #334155;
}

.config-info strong {
  color: #475569;
  font-weight: 600;
}

/* 按钮优化 */
:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 16px;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .page-header {
    padding: 24px;
  }
  
  .test-card {
    padding: 16px;
  }
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .header-title {
    font-size: 24px;
  }
}
</style>
