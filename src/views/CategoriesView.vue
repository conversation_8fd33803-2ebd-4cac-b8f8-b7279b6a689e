<template>
  <div class="categories-container">
    <!-- 分类列表 -->
    <div class="table-container material-card">
      <div class="table-header">
        <el-button type="primary" @click="openCreateDialog" class="add-button">
          <span class="add-icon">📂</span>
          <span class="add-text">添加分类</span>
        </el-button>
      </div>
      <el-table :data="categories" style="width: 100%" v-loading="loading">
        <el-table-column prop="categoryName" label="名称" min-width="200" show-overflow-tooltip />
        <el-table-column label="封面" width="100" align="center">
          <template #default="scope">
            <div class="cover-container">
              <el-image 
                v-if="scope.row.cover" 
                :src="scope.row.cover" 
                style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;"
                fit="cover"
                @error="handleImageError(scope.row)"
              >
                <template #error>
                  <div class="image-error">
                    <span>❌</span>
                  </div>
                </template>
              </el-image>
              <span v-else class="no-cover">📂</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="scope">
            <el-button text size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button text type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑分类对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑分类' : '添加分类'" 
      width="500px"
      @close="resetForm"
    >
      <el-form :model="form" label-width="80px" :rules="rules" ref="formRef">
        <el-form-item label="名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="封面文件" prop="coverFile">
          <el-upload 
            ref="coverUploadRef"
            action="#"
            :auto-upload="false"
            :on-change="handleCoverChange"
            :file-list="coverFileList"
            accept="image/*"
            style="width: 100%"
            :limit="1"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传 JPG/PNG 文件，最大5MB</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="uploading">
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { categoryApi, type Category } from '../services/api'

const dialogVisible = ref(false)
const loading = ref(false)
const uploading = ref(false)
const isEdit = ref(false)
const formRef = ref()
const coverUploadRef = ref()

const categories = ref<Category[]>([])
const coverFileList = ref<Array<{ raw: File; [key: string]: unknown }>>([])

const form = reactive({
  id: '',
  categoryName: '',
  coverFile: null as File | null,
})

const rules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 32, message: '分类名称长度在 1 到 32 个字符', trigger: 'blur' }
  ],
}

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true
  try {
    const response = await categoryApi.getCategoryList()
    if (response.success) {
      categories.value = response.data
      // 调试信息：打印分类数据
      console.log('=== 分类数据调试 ===')
      console.log('分类总数:', response.data.length)
      console.log('完整分类数据:', response.data)
      
      // 检查每个分类的封面字段
      response.data.forEach((category, index) => {
        console.log(`分类 ${index + 1} (${category.categoryName}):`)
        console.log('  - cover字段:', category.cover)
        console.log('  - cover类型:', typeof category.cover)
        console.log('  - cover是否为空:', !category.cover)
        if (category.cover) {
          console.log('  - 生成的URL:', category.cover)
        }
      })
    } else {
      ElMessage.error(response.message || '获取分类列表失败')
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 处理封面文件选择
const handleCoverChange = (file: { raw: File; [key: string]: unknown }) => {
  form.coverFile = file.raw
  coverFileList.value = [file]
}

// 打开创建对话框
const openCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

// 编辑分类
const handleEdit = async (row: Category) => {
  isEdit.value = true
  form.id = row.id
  form.categoryName = row.categoryName
  form.coverFile = null
  coverFileList.value = []
  
  // 如果有封面，显示当前封面
  if (row.cover) {
    coverFileList.value = [{
      name: row.cover,
      url: row.cover,
      raw: null as unknown as File
    }]
  }
  
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch {
    return
  }

  uploading.value = true
  try {
    let response
    if (isEdit.value) {
      // 更新分类
      response = await categoryApi.updateCategory(
        form.id, 
        form.categoryName, 
        form.coverFile || undefined
      )
    } else {
      // 创建分类
      response = await categoryApi.createCategory(
        form.categoryName, 
        form.coverFile || undefined
      )
    }

    if (response.success) {
      ElMessage.success(response.message || (isEdit.value ? '分类更新成功' : '分类添加成功'))
      dialogVisible.value = false
      resetForm()
      fetchCategories() // 刷新列表
    } else {
      ElMessage.error(response.message || (isEdit.value ? '分类更新失败' : '分类添加失败'))
    }
  } catch (error: unknown) {
    console.error('操作分类失败:', error)
    const errorMessage = error instanceof Error ? error.message : (isEdit.value ? '分类更新失败' : '分类添加失败')
    ElMessage.error(errorMessage)
  } finally {
    uploading.value = false
  }
}

// 删除分类
const handleDelete = async (row: Category) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个分类吗？如果该分类下有书籍，将无法删除。', 
      '提示', 
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await categoryApi.deleteCategory(row.id)
    if (response.success) {
      ElMessage.success(response.message || '删除成功')
      fetchCategories() // 刷新列表
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: unknown) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      const errorMessage = error instanceof Error ? error.message : '删除失败'
      ElMessage.error(errorMessage)
    }
  }
}

// 重置表单
const resetForm = () => {
  form.id = ''
  form.categoryName = ''
  form.coverFile = null
  coverFileList.value = []
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 格式化日期
const formatDate = (timestamp: string | number) => {
  if (!timestamp) return '-'

  let date: Date

  // 如果是ISO格式字符串 (如: 2025-06-29T12:24:31.000Z)
  if (typeof timestamp === 'string' && timestamp.includes('T')) {
    date = new Date(timestamp)
  } else {
    // 如果是数字时间戳，假设是秒级
    date = new Date(Number(timestamp) * 1000)
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return '-'
  }

  // 格式化为 yyyy-MM-dd HH:mm:ss
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 图片加载错误处理
const handleImageError = (category: Category) => {
  console.error('图片加载失败:')
  console.error('  - 分类名称:', category.categoryName)
  console.error('  - 封面字段:', category.cover)
  console.error('  - 生成的URL:', category.cover)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCategories()
})
</script>

<style scoped>
/* Material Design 风格卡片 */
.material-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05); /* Material Design Elevation 1 */
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.material-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.04), 0 0 4px rgba(0,0,0,0.06); /* Material Design Elevation 2 */
}

/* 容器 */
.categories-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
}

/* 表格容器 */
.table-container {
  overflow: hidden; /* 确保表格内部滚动条正常 */
}

.table-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  border: none;
  color: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(52, 211, 153, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.add-button:hover {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 6px 20px rgba(52, 211, 153, 0.4);
  transform: translateY(-2px);
}

.add-button:active {
  transform: translateY(0);
  box-shadow: 0 3px 8px rgba(52, 211, 153, 0.3);
}

.add-icon {
  font-size: 18px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.add-text {
  font-size: 14px;
  letter-spacing: 0.5px;
}

.no-cover {
  color: #9aa0a6;
  font-size: 12px;
}

.cover-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-error {
  width: 60px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  font-size: 12px;
}

/* 对话框优化 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}

.el-upload__tip {
  color: #64748b;
  font-size: 12px;
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}
</style>
