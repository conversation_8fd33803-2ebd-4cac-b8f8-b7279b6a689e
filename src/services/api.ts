import axios from 'axios'
import { config } from '../config/env'
import { EncryptionUtils } from '../utils/encryption'

// 统一响应接口
export interface ApiResponse<T = unknown> {
  success: boolean
  data: T
  message: string
  code: number
}

// 后端原始响应格式
interface BackendResponse<T = unknown> {
  code: number
  message: string
  data: T
}

// 创建axios实例
const api = axios.create({
  baseURL: config.apiBaseUrl,
  timeout: 30000, // 30秒超时
})

// 响应拦截器 - 只处理错误
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error)
    // 处理HTTP错误状态码
    const errorMessage = error.response?.data?.message || error.message || '网络请求失败'
    return Promise.reject(new Error(errorMessage))
  }
)

// 包装函数，将后端响应格式转换为前端期望的格式
function transformResponse<T>(backendResponse: BackendResponse<T>): ApiResponse<T> {
  return {
    success: backendResponse.code === 200,
    data: backendResponse.data,
    message: backendResponse.message,
    code: backendResponse.code
  }
}

// 通用请求方法
async function request<T>(requestPromise: Promise<{ data: BackendResponse<T> }>): Promise<ApiResponse<T>> {
  try {
    const response = await requestPromise
    return transformResponse(response.data)
  } catch (error) {
    throw error
  }
}

// 文件验证工具
export const fileUtils = {
  // 验证文件大小
  validateFileSize(file: File, maxSizeMB: number): boolean {
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    return file.size <= maxSizeBytes
  },

  // 验证文件类型
  validateFileType(file: File, allowedTypes: string[]): boolean {
    const extension = '.' + file.name.split('.').pop()?.toLowerCase()
    return allowedTypes.includes(extension || '')
  },

  // 验证PDF文件
  validatePdfFile(file: File): { valid: boolean; message?: string } {
    if (!this.validateFileType(file, config.allowedFileTypes.pdf)) {
      return { valid: false, message: '请选择PDF格式的文件' }
    }
    if (!this.validateFileSize(file, config.maxFileSize.pdf)) {
      return { valid: false, message: `PDF文件大小不能超过${config.maxFileSize.pdf}MB` }
    }
    return { valid: true }
  },

  // 验证图片文件
  validateImageFile(file: File): { valid: boolean; message?: string } {
    if (!this.validateFileType(file, config.allowedFileTypes.image)) {
      return { valid: false, message: '请选择图片格式的文件' }
    }
    if (!this.validateFileSize(file, config.maxFileSize.image)) {
      return { valid: false, message: `图片文件大小不能超过${config.maxFileSize.image}MB` }
    }
    return { valid: true }
  },
}

// 书籍相关接口
export interface Book {
  id: string
  title: string
  cover: string
  url: string
  level: number
  categoryId: string
  readCount: number
  favoriteCount: number
  createTime: string
  updateTime: string
  isFree: boolean
}

// 分类相关接口
export interface Category {
  id: string
  categoryName: string
  cover: string
  createTime: string
  updateTime: string
}

// 上传书籍的参数
export interface UploadBookParams {
  title: string
  categoryId: string
  isFree: boolean
  level: number
  pdfFile: File
  coverFile: File
}

// 书籍API服务
export class BookApi {
  // 上传书籍
  async uploadBook(params: UploadBookParams): Promise<ApiResponse<Book>> {
    // 验证文件
    const pdfValidation = fileUtils.validatePdfFile(params.pdfFile)
    if (!pdfValidation.valid) {
      throw new Error(pdfValidation.message)
    }

    const coverValidation = fileUtils.validateImageFile(params.coverFile)
    if (!coverValidation.valid) {
      throw new Error(coverValidation.message)
    }

    // 加密PDF文件
    const encryptedPdfFile = await EncryptionUtils.encryptFileObject(params.pdfFile)

    const formData = new FormData()
    formData.append('title', params.title)
    formData.append('categoryId', params.categoryId)
    formData.append('isFree', params.isFree.toString())
    formData.append('level', params.level.toString())
    formData.append('pdf', encryptedPdfFile)
    formData.append('cover', params.coverFile)

    return request(api.post('/api/v1/book', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }))
  }

  // 更新书籍
  async updateBook(bookId: string, params: Partial<UploadBookParams>): Promise<ApiResponse<Book>> {
    const formData = new FormData()
    
    // 添加基本字段
    if (params.title) formData.append('title', params.title)
    if (params.categoryId) formData.append('categoryId', params.categoryId)
    if (params.isFree !== undefined) formData.append('isFree', params.isFree.toString())
    if (params.level) formData.append('level', params.level.toString())
    
    // 验证并添加文件
    if (params.pdfFile) {
      const pdfValidation = fileUtils.validatePdfFile(params.pdfFile)
      if (!pdfValidation.valid) {
        throw new Error(pdfValidation.message)
      }
      // 加密PDF文件
      const encryptedPdfFile = await EncryptionUtils.encryptFileObject(params.pdfFile)
      formData.append('pdf', encryptedPdfFile)
    }
    
    if (params.coverFile) {
      const coverValidation = fileUtils.validateImageFile(params.coverFile)
      if (!coverValidation.valid) {
        throw new Error(coverValidation.message)
      }
      formData.append('cover', params.coverFile)
    }

    return request(api.post(`/api/v1/book/update/${bookId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }))
  }

  // 获取书籍列表
  async getBookList(page: number = 1, pageSize: number = 10): Promise<ApiResponse<Book[]>> {
    return request(api.get('/api/v1/book/list', {
      params: { page, pageSize },
    }))
  }

  // 获取书籍URL
  async getBookUrl(bookId: string): Promise<ApiResponse<string>> {
    return request(api.get(`/api/v1/book/${bookId}/url`))
  }

  // 删除书籍
  async deleteBook(bookId: string): Promise<ApiResponse<Record<string, unknown>>> {
    return request(api.post(`/api/v1/book/delete/${bookId}`))
  }
}

// 分类API服务
export class CategoryApi {
  // 获取分类列表
  async getCategoryList(): Promise<ApiResponse<Category[]>> {
    return request(api.get('/api/v1/category/list'))
  }

  // 创建分类
  async createCategory(name: string, coverFile?: File): Promise<ApiResponse<Category>> {
    const formData = new FormData()
    formData.append('categoryName', name)
    if (coverFile) {
      // 验证图片文件
      const validation = fileUtils.validateImageFile(coverFile)
      if (!validation.valid) {
        throw new Error(validation.message)
      }
      formData.append('cover', coverFile)
    }

    return request(api.post('/api/v1/category', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }))
  }

  // 更新分类
  async updateCategory(categoryId: string, name: string, coverFile?: File): Promise<ApiResponse<Category>> {
    const formData = new FormData()
    formData.append('categoryName', name)
    if (coverFile) {
      // 验证图片文件
      const validation = fileUtils.validateImageFile(coverFile)
      if (!validation.valid) {
        throw new Error(validation.message)
      }
      formData.append('cover', coverFile)
    }

    return request(api.post(`/api/v1/category/update/${categoryId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }))
  }

  // 删除分类
  async deleteCategory(categoryId: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return request(api.post(`/api/v1/category/delete/${categoryId}`))
  }

  // 根据ID获取分类
  async getCategoryById(categoryId: string): Promise<ApiResponse<Category>> {
    return request(api.get(`/api/v1/category/${categoryId}`))
  }
}

// 创建API实例
export const bookApi = new BookApi()
export const categoryApi = new CategoryApi() 