# 后端管理系统

> 该系统使用vue3开发，是一个后台管理dashboard，左边是功能列表，右边展示列表项对应的页面
技术栈：vue3 + pinia + axios + elementUI

## 功能特性

### 书籍管理
- 展示保存在 R2 中的所有书籍列表
- 上传书籍到 R2（支持PDF文件和封面图片）
- 编辑书籍信息（支持部分字段更新）
- 对书籍的增删改查
- 文件大小和类型验证
- 分页显示

### 分类管理
- 对分类的增删改查
- 支持分类封面图片上传
- 分类列表展示

## 项目结构

```
src/
├── components/          # 公共组件
├── views/              # 页面组件
│   ├── BooksView.vue   # 书籍管理页面
│   ├── CategoriesView.vue # 分类管理页面
│   └── TestView.vue    # API测试页面
├── services/           # API服务
│   └── api.ts         # 书籍和分类API
├── config/            # 配置文件
│   └── env.ts         # 环境配置
├── stores/            # 状态管理
├── router/            # 路由配置
└── assets/            # 静态资源
```

## 环境配置

### 开发环境
```bash
# 启动开发服务器
npm run dev

# 构建开发版本
npm run build:dev
```

### 生产环境
```bash
# 构建生产版本
npm run build:prod

# 预览生产版本
npm run preview:prod
```

### 环境变量配置

#### 开发环境 (.env.development)
```env
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:8787
VITE_APP_TITLE=后端管理系统 (开发版)
```

#### 生产环境 (.env.production)
```env
NODE_ENV=production
VITE_API_BASE_URL=https://kr-cfw.liusilong-lsl.workers.dev
VITE_APP_TITLE=后端管理系统
```

#### 自定义配置 (.env.local)
```env
# 复制 .env.example 为 .env.local 并根据需要修改
VITE_API_BASE_URL=你的API地址
```

## 开发环境设置

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 配置后端API地址
项目会自动根据环境选择API地址：
- 开发环境：`http://localhost:8787`
- 生产环境：`https://kr-cfw.liusilong-lsl.workers.dev`

## 后端API要求

### 书籍相关接口
- `POST /book` - 上传书籍
- `POST /book/update/:id` - 更新书籍
- `GET /book/list` - 获取书籍列表（支持分页）
- `GET /book/:id/url` - 获取书籍URL
- `DELETE /book/:id` - 删除书籍

### 分类相关接口
- `GET /category/list` - 获取分类列表
- `POST /category` - 创建分类
- `POST /category/update/:id` - 更新分类
- `POST /category/delete/:id` - 删除分类
- `GET /category/:id` - 根据ID获取分类

## 文件上传规范

### 书籍上传
- **PDF文件**: 最大50MB，仅支持.pdf格式
- **封面图片**: 最大5MB，支持jpg/jpeg/png/gif/webp格式
- **必需字段**: 名称、分类、PDF文件、封面文件
- **可选字段**: Level（默认1）、是否免费（默认false）

### 分类上传
- **封面图片**: 最大5MB，支持jpg/jpeg/png/gif/webp格式
- **必需字段**: 分类名称
- **可选字段**: 封面文件

## 技术特性

- ✅ **响应式设计**: 支持不同屏幕尺寸
- ✅ **文件验证**: 自动验证文件类型和大小
- ✅ **错误处理**: 完善的错误提示和处理
- ✅ **加载状态**: 上传和加载时的状态显示
- ✅ **表单验证**: 完整的表单验证规则
- ✅ **TypeScript**: 完整的类型定义
- ✅ **环境配置**: 开发和生产环境自动切换

## 构建部署

### 开发环境构建
```bash
npm run build:dev
```

### 生产环境构建
```bash
npm run build:prod
```

### 预览构建结果
```bash
# 预览开发版本
npm run preview

# 预览生产版本
npm run preview:prod
```

## 部署说明

### 静态文件部署
构建完成后，`dist` 目录包含所有静态文件，可以部署到：
- Nginx
- Apache
- CDN服务
- 静态文件托管服务

### 环境变量
确保生产环境正确配置了 `VITE_API_BASE_URL` 指向你的后端API地址。

## 注意事项

1. 确保后端API服务器正在运行
2. 检查API地址配置是否正确
3. 确保R2存储服务配置正确
4. 文件上传需要网络连接稳定
5. 生产环境需要HTTPS支持

## 开发计划

- [ ] 添加书籍编辑功能
- [ ] 添加分类编辑功能
- [ ] 添加用户权限管理
- [ ] 添加搜索和筛选功能
- [ ] 添加批量操作功能
- [ ] 添加数据导出功能

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```
