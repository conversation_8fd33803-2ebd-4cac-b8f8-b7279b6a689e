{"name": "kr-cfw-dashboard", "version": "1.0.0", "description": "后端管理系统 - 书籍和分类管理", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "prod": "vite --mode production", "build": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.10.0", "crypto-js": "^4.2.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^5.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}